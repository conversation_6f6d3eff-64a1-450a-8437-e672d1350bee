import { <PERSON>mponent, OnInit, OnDestroy, OnChanges, SimpleChanges, Input, Output, EventEmitter, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, FormArray, FormControl, Validators } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil, startWith, map } from 'rxjs/operators';

// Material modules
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { MatChipsModule } from '@angular/material/chips';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';


// Services
import { SmartDashboardService } from '../../services/smart-dashboard.service';
import { AuthService } from '../../services/auth.service';

export interface CategoryWorkareaMapping {
  categoryName: string;
  workAreas: string[];
}

export interface WorkAreaData {
  restaurantIdOld: string;
  branchName: string;
  workAreas: string[];
  disabled: boolean;
}

@Component({
  selector: 'app-category-workarea-mapping',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatSelectModule,
    MatInputModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatDividerModule,
    MatChipsModule,
    MatCheckboxModule,
    NgxMatSelectSearchModule
  ],
  templateUrl: './category-workarea-mapping.component.html',
  styleUrls: ['./category-workarea-mapping.component.scss']
})
export class CategoryWorkareaMappingComponent implements OnInit, OnChanges, OnDestroy {
  @Input() tenantId: string = '';
  @Input() showAsDialog: boolean = false;
  @Input() categories: string[] = [];
  @Input() selectedCategoryNames: string[] = [];
  @Input() workAreas: WorkAreaData[] = [];
  @Input() existingMappings: CategoryWorkareaMapping[] = [];
  @Output() mappingsChanged = new EventEmitter<CategoryWorkareaMapping[]>();
  @Output() closeDialog = new EventEmitter<void>();

  // Form and data
  mappingForm: FormGroup;
  mappings: CategoryWorkareaMapping[] = [];
  selectedCategories: string[] = [];
  allWorkAreas: string[] = [];

  // Multi-select category controls
  selectedCategoriesCtrl = new FormControl<string[]>([]);
  categoryFilterCtrl = new FormControl<string>('');
  filteredCategories: string[] = [];

  // UI state
  isLoading = false;
  isSaving = false;
  validationErrors: string[] = [];

  // Destroy subject for cleanup
  private destroy$ = new Subject<void>();

  constructor(
    private fb: FormBuilder,
    private smartDashboardService: SmartDashboardService,
    private authService: AuthService,
    private snackBar: MatSnackBar,
    private cdr: ChangeDetectorRef
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    if (!this.tenantId) {
      const user = this.authService.getCurrentUser();
      this.tenantId = user?.tenantId || '';
    }

    // Initialize category filtering
    this.initializeCategoryFiltering();

    // Use input properties if provided, otherwise load data
    if (this.categories.length > 0 && this.workAreas.length > 0) {
      this.updateSelectedCategories();
      this.extractAllWorkAreas();
      this.mappings = this.existingMappings || [];
      this.buildFormFromMappings();
    } else {
      this.loadCategories();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedCategoryNames'] && !changes['selectedCategoryNames'].firstChange) {
      this.selectedCategoriesCtrl.setValue(this.selectedCategoryNames);
      this.updateSelectedCategories();
      this.buildFormFromSelectedCategories();
    }

    if (changes['workAreas'] && !changes['workAreas'].firstChange) {
      this.extractAllWorkAreas();
      this.buildFormFromSelectedCategories();
    }

    if (changes['categories'] && !changes['categories'].firstChange) {
      this.filteredCategories = [...this.categories];
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }



  // ===== INITIALIZATION =====
  private initializeForm(): void {
    this.mappingForm = this.fb.group({
      mappings: this.fb.array([])
    });
  }

  get mappingsFormArray(): FormArray {
    return this.mappingForm.get('mappings') as FormArray;
  }

  // ===== DATA LOADING =====
  private loadCategories(): void {
    this.smartDashboardService.getCategories(this.tenantId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response && response.categories) {
            this.categories = response.categories.map((cat: any) => cat.name || cat.categoryName);
            this.filteredCategories = [...this.categories];
            this.updateSelectedCategories();
            this.extractAllWorkAreas();
            this.mappings = this.existingMappings || [];
            this.buildFormFromMappings();

          }
          this.isLoading = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showError('Failed to load categories');
          this.isLoading = false;
          this.cdr.detectChanges();
        }
      });
  }

  // ===== WORK AREAS MANAGEMENT =====
  private extractAllWorkAreas(): void {
    this.allWorkAreas = [];
    this.workAreas.forEach(workAreaData => {
      if (workAreaData.workAreas && workAreaData.workAreas.length > 0) {
        this.allWorkAreas.push(...workAreaData.workAreas);
      }
    });
    // Remove duplicates
    this.allWorkAreas = [...new Set(this.allWorkAreas)];
  }

  // ===== FORM MANAGEMENT =====
  private buildFormFromMappings(): void {
    // For the two-panel layout, we just need to trigger change detection
    // The mappings are managed directly through the selectedCategory and mappings array
    this.cdr.detectChanges();
  }

  // ===== CATEGORY FILTERING MANAGEMENT =====
  private initializeCategoryFiltering(): void {
    // Initialize filtered categories
    this.filteredCategories = [...this.categories];

    // Set up category filtering
    this.categoryFilterCtrl.valueChanges
      .pipe(
        startWith(''),
        map(value => this.filterCategories(value || '')),
        takeUntil(this.destroy$)
      )
      .subscribe(filtered => {
        this.filteredCategories = filtered;
        this.cdr.detectChanges();
      });

    // Listen to category selection changes
    this.selectedCategoriesCtrl.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(selectedCategories => {
        this.selectedCategoryNames = selectedCategories || [];
        this.updateSelectedCategories();
        this.buildFormFromSelectedCategories();
      });
  }

  private filterCategories(value: string): string[] {
    const filterValue = value.toLowerCase();
    return this.categories.filter(category =>
      category.toLowerCase().includes(filterValue)
    );
  }

  private updateSelectedCategories(): void {
    this.selectedCategories = this.categories.filter(category =>
      this.selectedCategoryNames.includes(category)
    );
  }



  private buildFormFromSelectedCategories(): void {
    // Store current form values to preserve user selections
    const currentFormValues = new Map<string, string[]>();
    for (let i = 0; i < this.mappingsFormArray.length; i++) {
      const formGroup = this.mappingsFormArray.at(i) as FormGroup;
      const categoryName = formGroup.get('categoryName')?.value;
      const workAreas = formGroup.get('workAreas')?.value || [];
      if (categoryName) {
        currentFormValues.set(categoryName, workAreas);
      }
    }

    // Get currently selected category names for comparison
    const currentCategoryNames = new Set(
      Array.from({ length: this.mappingsFormArray.length }, (_, i) => {
        const formGroup = this.mappingsFormArray.at(i) as FormGroup;
        return formGroup.get('categoryName')?.value;
      }).filter(Boolean)
    );

    const newCategoryNames = new Set(this.selectedCategories);

    // Remove categories that are no longer selected
    for (let i = this.mappingsFormArray.length - 1; i >= 0; i--) {
      const formGroup = this.mappingsFormArray.at(i) as FormGroup;
      const categoryName = formGroup.get('categoryName')?.value;
      if (!newCategoryNames.has(categoryName)) {
        this.mappingsFormArray.removeAt(i);
      }
    }

    // Add new categories that were just selected
    this.selectedCategories.forEach(categoryName => {
      if (!currentCategoryNames.has(categoryName)) {
        // Find existing mapping for this category
        const existingMapping = this.mappings.find(m => m.categoryName === categoryName);

        // Check if we have current form values for this category (from previous selections)
        const preservedWorkAreas = currentFormValues.get(categoryName) || [];

        const mapping: CategoryWorkareaMapping = existingMapping || {
          categoryName: categoryName,
          workAreas: preservedWorkAreas // Preserve user selections or start empty
        };

        // If we have preserved work areas, use them instead of existing mapping
        if (preservedWorkAreas.length > 0) {
          mapping.workAreas = preservedWorkAreas;
        }

        this.mappingsFormArray.push(this.createMappingFormGroup(mapping));
      }
    });

    // Sort form array to match the order of selected categories
    const sortedControls: FormGroup[] = [];
    this.selectedCategories.forEach(categoryName => {
      const control = this.mappingsFormArray.controls.find(ctrl => {
        const formGroup = ctrl as FormGroup;
        return formGroup.get('categoryName')?.value === categoryName;
      }) as FormGroup;
      if (control) {
        sortedControls.push(control);
      }
    });

    // Clear and rebuild with sorted controls
    while (this.mappingsFormArray.length !== 0) {
      this.mappingsFormArray.removeAt(0);
    }
    sortedControls.forEach(control => {
      this.mappingsFormArray.push(control);
    });

    // Trigger change detection
    this.cdr.detectChanges();
  }

  private createMappingFormGroup(mapping: CategoryWorkareaMapping): FormGroup {
    return this.fb.group({
      categoryName: [mapping.categoryName, Validators.required],
      workAreas: [mapping.workAreas || []]
    });
  }



  // ===== VALIDATION =====
  private validateMappings(): void {
    this.validationErrors = [];

    // Check if at least one category has work areas mapped
    const hasAnyMapping = this.mappings.some(mapping =>
      mapping.workAreas && mapping.workAreas.length > 0
    );

    // Only show validation error if there are selected categories but no mappings
    if (!hasAnyMapping && this.selectedCategories.length > 0) {
      this.validationErrors.push('At least one category must have work areas selected');
    }
  }

  // ===== SAVE FUNCTIONALITY =====
  saveMappings(): void {
    this.validateMappings();
    if (this.validationErrors.length > 0) {
      this.showError('Please fix validation errors before saving');
      return;
    }

    this.isSaving = true;

    // Filter out empty mappings
    const validMappings = this.mappings.filter(m =>
      m.categoryName && m.workAreas.length > 0
    );

    // For now, just emit the mappings - actual save implementation depends on backend API
    setTimeout(() => {
      this.mappingsChanged.emit(validMappings);
      this.showSuccess('Mappings saved successfully');
      this.isSaving = false;
      this.cdr.detectChanges();
    }, 1000);
  }



  // ===== UI HELPERS =====
  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  onClose(): void {
    this.closeDialog.emit();
  }



  // ===== UTILITY METHODS =====
  getCategoryName(categoryName: string): string {
    return categoryName || 'Unknown Category';
  }

  // ===== TRACK BY METHODS =====
  trackByCategory(_index: number, category: string): string {
    return category;
  }

  trackByWorkArea(_index: number, workArea: string): string {
    return workArea;
  }

  trackByCategoryName(index: number, item: any): string {
    const formGroup = item as FormGroup;
    return formGroup.get('categoryName')?.value || index.toString();
  }







  // ===== MULTI-SELECT CATEGORY METHODS =====

  /**
   * Toggle all categories selection
   */
  toggleAllCategories(event: any): void {
    if (event.checked) {
      this.selectedCategoriesCtrl.setValue([...this.filteredCategories]);
    } else {
      this.selectedCategoriesCtrl.setValue([]);
    }
  }

  /**
   * Check if all categories are selected
   */
  areAllCategoriesSelected(): boolean {
    const selectedCount = this.selectedCategoriesCtrl.value?.length || 0;
    return selectedCount === this.filteredCategories.length && this.filteredCategories.length > 0;
  }

  /**
   * Check if some categories are selected (for indeterminate state)
   */
  isSomeCategoriesSelected(): boolean {
    const selectedCount = this.selectedCategoriesCtrl.value?.length || 0;
    return selectedCount > 0 && selectedCount < this.filteredCategories.length;
  }

  /**
   * Check if a specific category is selected
   */
  isCategorySelected(category: string): boolean {
    const selectedCategories = this.selectedCategoriesCtrl.value || [];
    return selectedCategories.includes(category);
  }

  /**
   * Toggle individual category selection
   */
  toggleCategory(category: string, event: any): void {
    const selectedCategories = this.selectedCategoriesCtrl.value || [];

    if (event.checked) {
      // Add category if not already selected
      if (!selectedCategories.includes(category)) {
        this.selectedCategoriesCtrl.setValue([...selectedCategories, category]);
      }
    } else {
      // Remove category
      const updatedCategories = selectedCategories.filter(c => c !== category);
      this.selectedCategoriesCtrl.setValue(updatedCategories);
    }
  }

  onMappingsChanged(mappings: CategoryWorkareaMapping[]): void {
    this.mappings = mappings;
    this.mappingsChanged.emit(mappings);
  }

  /**
   * Get selected work areas for a specific category
   */
  getSelectedWorkAreasForCategory(categoryName: string): string[] {
    const mapping = this.mappings.find(m => m.categoryName === categoryName);
    return mapping ? mapping.workAreas : [];
  }



  // ===== FORM ACTIONS =====
  onWorkAreasChange(index: number, workAreas: string[]): void {
    const mappingGroup = this.mappingsFormArray.at(index) as FormGroup;
    if (mappingGroup) {
      mappingGroup.patchValue({ workAreas });

      // Update the internal mappings array to keep it in sync
      const categoryName = mappingGroup.get('categoryName')?.value;
      if (categoryName) {
        const existingMappingIndex = this.mappings.findIndex(m => m.categoryName === categoryName);
        if (existingMappingIndex >= 0) {
          this.mappings[existingMappingIndex].workAreas = workAreas;
        } else {
          // Add new mapping if it doesn't exist
          this.mappings.push({
            categoryName,
            workAreas
          });
        }
      }

      this.cdr.detectChanges();
      this.validateMappings();
    }
  }

  getWorkAreasPlaceholder(index: number): string {
    const mappingGroup = this.mappingsFormArray.at(index) as FormGroup;
    const selectedWorkAreas = mappingGroup?.get('workAreas')?.value || [];
    const availableCount = this.getAvailableWorkAreas(index).length;

    if (selectedWorkAreas.length === 0) {
      return `Select work areas (0/${availableCount})`;
    }

    return `${selectedWorkAreas.length} selected (${availableCount} available)`;
  }

  getAvailableWorkAreas(categoryIndex: number): string[] {
    // Get all work areas that are already used by OTHER categories
    const usedWorkAreas = new Set<string>();

    for (let i = 0; i < this.mappingsFormArray.length; i++) {
      if (i !== categoryIndex) {
        const formGroup = this.mappingsFormArray.at(i) as FormGroup;
        const workAreas = formGroup.get('workAreas')?.value || [];
        workAreas.forEach((wa: string) => usedWorkAreas.add(wa));
      }
    }

    // Get currently selected work areas for this category
    const currentFormGroup = this.mappingsFormArray.at(categoryIndex) as FormGroup;
    const currentlySelected = new Set(currentFormGroup?.get('workAreas')?.value || []);

    // Return work areas that are either:
    // 1. Not used by other categories, OR
    // 2. Currently selected by this category (to allow deselection)
    return this.allWorkAreas.filter(wa => !usedWorkAreas.has(wa) || currentlySelected.has(wa));
  }



  // ===== GETTERS =====
  get canSave(): boolean {
    return this.mappings.length > 0 && !this.isSaving && this.validationErrors.length === 0;
  }

  get hasValidationErrors(): boolean {
    return this.validationErrors.length > 0;
  }
}
