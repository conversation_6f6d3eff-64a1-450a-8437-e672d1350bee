<div class="department-mapping-container" [class.dialog-mode]="showAsDialog">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <!-- Main Content -->
  <div *ngIf="!isLoading" class="mapping-content">
    <!-- Validation Errors -->
    <div *ngIf="hasValidationErrors" class="validation-errors">
      <mat-icon class="error-icon">error</mat-icon>
      <div class="error-list">
        <p *ngFor="let error of validationErrors" class="error-message">{{ error }}</p>
      </div>
    </div>

    <!-- Form -->
    <form [formGroup]="mappingForm" class="mapping-form">
      <div formArrayName="mappings" class="mappings-container">
        <!-- Empty State Message -->
        <div *ngIf="mappingsFormArray.controls.length === 0" class="empty-state-message">
          <mat-icon class="empty-icon">arrow_back</mat-icon>
          <p>Select categories from the left to configure work area mappings</p>
        </div>

        <!-- Mapping Rows -->
        <div
          *ngFor="let mappingGroup of mappingsFormArray.controls; let i = index; trackBy: trackByCategoryName"
          [formGroupName]="i"
          class="mapping-row"
        >
          <div class="category-name">
            <mat-icon class="category-icon">category</mat-icon>
            <span>{{ mappingGroup.get('categoryName')?.value || 'Unknown Category' }}</span>
          </div>

          <div class="workareas-dropdown">
            <mat-form-field appearance="outline" class="workareas-field">
              <mat-select
                formControlName="workAreas"
                multiple
                [placeholder]="getWorkAreasPlaceholder(i)"
                (selectionChange)="onWorkAreasChange(i, $event.value)"
                [disabled]="allWorkAreas.length === 0"
              >
                <mat-option
                  *ngFor="let workArea of getAvailableWorkAreas(i); trackBy: trackByWorkArea"
                  [value]="workArea"
                >
                  {{ workArea }}
                </mat-option>
              </mat-select>
              <mat-hint *ngIf="allWorkAreas.length === 0">No work areas available</mat-hint>
            </mat-form-field>
          </div>
        </div>
      </div>
    </form>
  </div>

  <!-- Actions -->
  <div class="mapping-actions">
    <button
      mat-button
      type="button"
      (click)="onClose()"
      *ngIf="showAsDialog"
      class="cancel-button"
    >
      Cancel
    </button>

    <button
      mat-raised-button
      color="primary"
      type="button"
      (click)="saveMappings()"
      [disabled]="!canSave"
      class="save-button"
    >
      <mat-spinner *ngIf="isSaving" diameter="20" class="button-spinner"></mat-spinner>
      <mat-icon *ngIf="!isSaving">save</mat-icon>
      {{ isSaving ? 'Saving...' : 'Save Mappings' }}
    </button>
  </div>
</div>

<!-- ################## REFERENCE CODE - REPLACE THE ABOVE LOGIC WITH THIS BELOW SKELTON ######################### -->